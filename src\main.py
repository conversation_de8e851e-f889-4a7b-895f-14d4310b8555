"""
AutoPilot AI - 主应用入口

FastAPI应用的主入口点，配置路由、中间件、异常处理等。
"""
import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager

from src.core.config import get_settings
from src.core.logger import get_logger
from src.api.itinerary.router import router as itinerary_router
from src.api.travel_planner import router as travel_planner_router
from src.api.ai_vlog import router as ai_vlog_router
from src.database.mongodb_client import get_mongo_client, close_mongo_client

# 获取配置和日志
settings = get_settings()
logger = get_logger("main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("AutoPilot AI 应用启动中...")
    
    try:
        # 尝试初始化数据库连接（允许失败）
        try:
            await get_mongo_client()
            logger.info("数据库连接已建立")
        except Exception as e:
            logger.warning(f"MongoDB连接失败，但应用将继续运行: {str(e)}")

        # 这里可以添加其他启动时的初始化逻辑
        logger.info("应用初始化完成")
        yield
    finally:
        # 关闭时的清理
        logger.info("AutoPilot AI 应用关闭中...")
        
        # 关闭数据库连接
        await close_mongo_client()
        logger.info("数据库连接已关闭")
        
        logger.info("应用清理完成")


# 创建FastAPI应用实例
app = FastAPI(
    title="AutoPilot AI - 旅行规划Agent",
    description="智能旅行规划Agent系统 - 提供AI驱动的个性化旅行规划服务",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "内部服务器错误",
            "detail": str(exc) if settings.app.environment == "development" else "服务暂时不可用"
        }
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        mongo_client = await get_mongo_client()
        
        return {
            "status": "healthy",
            "service": "AutoPilot AI - 旅行规划Agent",
            "version": "0.1.0",
            "components": {
                "database": "connected",
                "agent": "ready"
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "AutoPilot AI - 旅行规划Agent",
                "version": "0.1.0",
                "error": str(e)
            }
        )


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "欢迎使用 AutoPilot AI 旅行规划Agent",
        "description": "智能AI驱动的个性化旅行规划服务",
        "docs": "/docs",
        "health": "/health",
        "features": [
            "智能意图理解",
            "个性化推荐",
            "实时规划进度",
            "高德地图集成",
            "记忆学习系统"
        ]
    }


# 注册路由
app.include_router(itinerary_router)  # 暂时注释掉，因为该模块不存在
app.include_router(travel_planner_router)
app.include_router(ai_vlog_router)

# 挂载静态文件（用于前端页面）
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except Exception:
    # 如果static目录不存在，忽略错误
    pass


if __name__ == "__main__":
    # 运行应用
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )