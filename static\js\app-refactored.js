/**
 * AutoPilot AI - 重构版前端应用
 * 
 * 支持两阶段交互模式和TTS播报的现代化界面
 */

class TravelPlannerAppRefactored {
    constructor() {
        this.currentTraceId = null;
        this.eventSource = null;
        this.currentItinerary = null;
        this.currentPhase = 'waiting'; // waiting, analysis, planning, completed
        
        // 分析步骤状态
        this.analysisSteps = {
            'user_intent': { completed: false, title: '解析用户需求和画像' },
            'poi_preference': { completed: false, title: '景点偏好类型' },
            'food_preference': { completed: false, title: '美食偏好' },
            'accommodation_preference': { completed: false, title: '住宿偏好' }
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupViewModes();
        this.loadUserHistory();
        this.updateUI();
    }
    
    bindEvents() {
        // 规划表单提交
        document.getElementById('planningForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startPlanning();
        });
        
        // 视图模式切换
        document.getElementById('viewModeList').addEventListener('click', () => {
            this.switchViewMode('list');
        });
        
        document.getElementById('viewModeMap').addEventListener('click', () => {
            this.switchViewMode('map');
        });
        
        // 立即规划按钮
        const startPlanningBtn = document.getElementById('startPlanningBtn');
        if (startPlanningBtn) {
            startPlanningBtn.addEventListener('click', () => {
                this.startItineraryPlanning();
            });
        }
        
        // 取消规划按钮
        const cancelPlanningBtn = document.getElementById('cancelPlanningBtn');
        if (cancelPlanningBtn) {
            cancelPlanningBtn.addEventListener('click', () => {
                this.cancelPlanning();
            });
        }
        
        // 行程操作按钮
        document.getElementById('saveItinerary').addEventListener('click', () => {
            this.saveItinerary();
        });
        
        document.getElementById('editItinerary').addEventListener('click', () => {
            this.editItinerary();
        });
        
        document.getElementById('shareItinerary').addEventListener('click', () => {
            this.shareItinerary();
        });
        
        // 历史行程按钮
        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });
    }
    
    setupViewModes() {
        // 默认显示列表视图
        this.switchViewMode('list');
    }
    
    updateUI() {
        // 根据当前阶段更新UI显示
        this.hideAllViews();
        
        switch (this.currentPhase) {
            case 'waiting':
                this.showWaitingView();
                break;
            case 'analysis':
                this.showAnalysisView();
                break;
            case 'planning':
                this.showPlanningView();
                break;
            case 'completed':
                this.showCompletedView();
                break;
        }
    }
    
    hideAllViews() {
        document.getElementById('waitingView').style.display = 'none';
        document.getElementById('analysisView').style.display = 'none';
        document.getElementById('itineraryView').style.display = 'none';
    }
    
    showWaitingView() {
        document.getElementById('waitingView').style.display = 'flex';
    }
    
    showAnalysisView() {
        document.getElementById('analysisView').style.display = 'flex';
        
        // 更新分析状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');
        
        if (title) title.textContent = '正在分析您的需求...';
        if (desc) desc.textContent = 'AI正在理解您的旅行偏好和需求';
    }
    
    showPlanningView() {
        document.getElementById('analysisView').style.display = 'flex';
        
        // 更新规划状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');
        
        if (title) title.textContent = '正在生成旅行方案...';
        if (desc) desc.textContent = 'AI正在为您规划详细的行程安排';
    }
    
    showCompletedView() {
        document.getElementById('itineraryView').style.display = 'block';
    }
    
    async startPlanning() {
        const query = document.getElementById('userQuery').value.trim();
        const userId = document.getElementById('userId').value.trim() || '1';

        if (!query) {
            this.showAlert('请输入您的旅行想法', 'warning');
            return;
        }

        try {
            // 切换到分析阶段
            this.currentPhase = 'analysis';
            this.updateUI();

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始分析您的旅行需求');
            }

            // 重置分析步骤状态
            this.resetAnalysisSteps();

            // 开始真实的SSE连接
            await this.startRealPlanning(query, userId);

        } catch (error) {
            console.error('规划失败:', error);
            this.showAlert('规划失败: ' + error.message, 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        }
    }
    
    resetAnalysisSteps() {
        Object.keys(this.analysisSteps).forEach(stepKey => {
            this.analysisSteps[stepKey].completed = false;
            const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
            if (stepElement) {
                stepElement.classList.remove('completed');
                stepElement.classList.remove('active');
                
                // 重置结果显示
                const resultElement = stepElement.querySelector('.analysis-result');
                if (resultElement) {
                    resultElement.innerHTML = '<div class="loading-placeholder">等待分析...</div>';
                }
                
                // 显示加载状态
                const statusElement = stepElement.querySelector('.analysis-status');
                if (statusElement) {
                    statusElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
                }
            }
        });
    }
    
    // 模拟分析过程已移除，现在使用真实的SSE连接
    
    setAnalysisStepActive(stepKey) {
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.add('active');
            stepElement.classList.remove('completed');
        }
    }
    
    completeAnalysisStep(stepKey, content) {
        this.analysisSteps[stepKey].completed = true;
        
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.remove('active');
            stepElement.classList.add('completed');
            
            // 更新结果显示
            const resultElement = stepElement.querySelector('.analysis-result');
            if (resultElement) {
                resultElement.innerHTML = `<div class="analysis-content-text">${content}</div>`;
            }
            
            // 更新状态图标
            const statusElement = stepElement.querySelector('.analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
            }
        }
    }
    
    showStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        const cancelBtn = document.getElementById('cancelPlanningBtn');
        
        if (startBtn) {
            startBtn.style.display = 'block';
            startBtn.classList.add('animate__animated', 'animate__fadeInUp');
        }
        
        if (cancelBtn) {
            cancelBtn.style.display = 'inline-block';
        }
        
        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('分析完成，点击立即规划开始生成行程');
        }
    }
    
    async startItineraryPlanning() {
        try {
            // 切换到规划阶段
            this.currentPhase = 'planning';
            this.updateUI();

            // 隐藏按钮
            document.getElementById('startPlanningBtn').style.display = 'none';

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始生成详细的旅行行程');
            }

            // 启动第二阶段的规划流程
            await this.startPlanningPhase();

        } catch (error) {
            console.error('行程规划失败:', error);
            this.showAlert('行程规划失败: ' + error.message, 'danger');
        }
    }

    async startPlanningPhase() {
        try {
            // 关闭之前的SSE连接
            if (this.eventSource) {
                this.eventSource.close();
                this.eventSource = null;
            }

            // 获取当前的查询和用户ID
            const query = document.getElementById('userQuery').value.trim();
            const userId = document.getElementById('userId').value.trim() || '1';

            // 创建新的SSE连接 - 执行规划阶段
            const url = `/api/travel/plan/${this.currentTraceId}/stream?user_id=${userId}&query=${encodeURIComponent(query)}&phase=planning`;
            this.eventSource = new EventSource(url);

            // 处理SSE事件
            this.eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleSSEEvent(data);
                } catch (error) {
                    console.error('解析SSE事件失败:', error);
                }
            };

            this.eventSource.onerror = (error) => {
                console.error('SSE连接错误:', error);
                this.eventSource.close();
                this.showAlert('连接服务器失败，请重试', 'danger');
                this.currentPhase = 'waiting';
                this.updateUI();
            };

        } catch (error) {
            console.error('启动规划阶段失败:', error);
            this.showAlert('启动规划阶段失败: ' + error.message, 'danger');
        }
    }

    // 模拟行程生成已移除，现在使用真实的SSE连接
    
    displayItinerary(itinerary) {
        // 更新行程标题和描述
        document.getElementById('itineraryTitle').textContent = itinerary.title;
        document.getElementById('itineraryDescription').textContent = itinerary.description;
        
        // 更新统计信息
        document.getElementById('totalDays').textContent = itinerary.days;
        document.getElementById('totalPOIs').textContent = itinerary.totalPOIs;
        document.getElementById('estimatedBudget').textContent = itinerary.estimatedBudget;
        document.getElementById('weatherInfo').textContent = itinerary.weather;
        
        this.currentItinerary = itinerary;
    }
    
    cancelPlanning() {
        // 停止当前规划
        this.currentPhase = 'waiting';
        this.updateUI();
        
        // 重置表单
        document.getElementById('userQuery').value = '';
        
        // 隐藏按钮
        document.getElementById('startPlanningBtn').style.display = 'none';
        document.getElementById('cancelPlanningBtn').style.display = 'none';
        
        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('规划已取消');
        }
        
        this.showAlert('规划已取消', 'info');
    }
    
    switchViewMode(mode) {
        const listBtn = document.getElementById('viewModeList');
        const mapBtn = document.getElementById('viewModeMap');
        const itineraryView = document.getElementById('itineraryView');
        const mapView = document.getElementById('mapView');
        
        if (mode === 'list') {
            listBtn.classList.add('active');
            mapBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'block';
            if (mapView) mapView.style.display = 'none';
        } else {
            mapBtn.classList.add('active');
            listBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'none';
            if (mapView) mapView.style.display = 'block';
        }
    }

    async startRealPlanning(query, userId) {
        // 生成trace_id
        this.currentTraceId = 'trace_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // 创建SSE连接 - 只执行分析阶段
        const url = `/api/travel/plan/${this.currentTraceId}/stream?user_id=${userId}&query=${encodeURIComponent(query)}&phase=analysis`;
        this.eventSource = new EventSource(url);

        // 处理SSE事件
        this.eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleSSEEvent(data);
            } catch (error) {
                console.error('解析SSE事件失败:', error);
            }
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.eventSource.close();
            this.showAlert('连接服务器失败，请重试', 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        };
    }

    handleSSEEvent(data) {
        console.log('收到SSE事件:', data);

        const eventType = data.event_type;
        const payload = data.payload;

        switch (eventType) {
            case 'thinking_step':
                this.handleThinkingStep(payload);
                break;
            case 'tool_call':
                this.handleToolCall(payload);
                break;
            case 'tool_result':
                this.handleToolResult(payload);
                break;
            case 'final_itinerary':
                this.handleFinalItinerary(payload);
                break;
            case 'complete':
                this.handleComplete(payload);
                break;
            case 'error':
                this.handleError(payload);
                break;
            default:
                console.log('未知事件类型:', eventType);
        }
    }

    handleComplete(payload) {
        console.log('处理完成事件:', payload);

        // 如果当前是分析阶段，显示立即规划按钮
        if (this.currentPhase === 'analysis') {
            console.log('分析阶段完成，显示立即规划按钮');
            this.showStartPlanningButton();
        }

        // 关闭当前的SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    handleThinkingStep(payload) {
        // 根据思考步骤更新UI
        console.log('处理思考步骤:', payload);

        // 映射后端的中文分类到前端的步骤
        const categoryMapping = {
            '出行对象': 'user_intent',
            '其他': 'user_intent', // 用户画像分析归类为"其他"，映射到用户需求
            '景点推荐': 'poi_preference',
            '美食推荐': 'food_preference',
            '住宿推荐': 'accommodation_preference'
        };

        const stepKey = categoryMapping[payload.category];
        if (stepKey) {
            this.updateAnalysisStep(stepKey, payload.content);
        } else {
            console.log('未映射的分类:', payload.category, payload.content);
        }



        // 检查是否是分析完成的信号
        if (payload.content && payload.content.includes('分析阶段完成')) {
            console.log('检测到分析阶段完成，显示立即规划按钮');
            this.showStartPlanningButton();
        }
    }

    handleToolCall(payload) {
        console.log('工具调用:', payload.tool_name, payload.parameters);
    }

    handleToolResult(payload) {
        console.log('工具结果:', payload.tool_name, payload.success);
    }

    handleFinalItinerary(payload) {
        // 显示最终行程
        this.displayRealItinerary(payload);
        this.currentPhase = 'completed';
        this.updateUI();

        // 关闭SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        // TTS播报
        if (window.ttsManager && payload.summary) {
            window.ttsManager.speakItineraryInfo(payload.summary.title, payload.summary.description);
        }
    }

    handleError(payload) {
        console.error('规划错误:', payload);
        this.showAlert('规划过程中发生错误: ' + payload.error_message, 'danger');
        this.currentPhase = 'waiting';
        this.updateUI();

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    updateAnalysisStep(stepKey, content) {
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            // 标记为活跃状态
            stepElement.classList.add('active');

            // 更新结果内容
            const resultElement = stepElement.querySelector('.analysis-result');
            if (resultElement) {
                resultElement.innerHTML = content;
            }

            // 更新状态为完成
            const statusElement = stepElement.querySelector('.analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            }

            // 标记为完成
            stepElement.classList.add('completed');
            this.analysisSteps[stepKey].completed = true;

            // 检查是否所有分析步骤都完成了
            const allStepsCompleted = ['user_intent', 'poi_preference', 'food_preference', 'accommodation_preference']
                .every(key => this.analysisSteps[key].completed);

            if (allStepsCompleted) {
                // 显示用户画像
                this.showUserProfile();
            }

            // 检查是否所有步骤都完成了
            const allCompleted = Object.values(this.analysisSteps).every(step => step.completed);
            if (allCompleted) {
                this.showStartPlanningButton();
            }
        }
    }

    showUserProfile() {
        console.log('showUserProfile方法被调用');
        // 显示用户画像项
        const userProfileItem = document.getElementById('userProfileItem');
        console.log('userProfileItem元素:', userProfileItem);
        if (userProfileItem) {
            userProfileItem.style.display = 'flex';
            userProfileItem.classList.add('active');
            console.log('用户画像元素已显示');

            // 从后端获取用户画像数据
            this.fetchUserProfile();
        } else {
            console.error('找不到userProfileItem元素');
        }
    }

    async fetchUserProfile() {
        console.log('fetchUserProfile方法被调用');
        try {
            console.log('开始获取用户画像数据');
            const response = await fetch('/api/travel/user_profile', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            console.log('API响应状态:', response.status);
            if (response.ok) {
                const userProfile = await response.json();
                console.log('获取到用户画像数据:', userProfile);
                this.displayUserProfile(userProfile);
            } else {
                console.error('获取用户画像失败:', response.statusText);
                // 使用模拟数据作为后备
                this.displayUserProfile(this.getMockUserProfile());
            }
        } catch (error) {
            console.error('获取用户画像出错:', error);
            // 使用模拟数据作为后备
            this.displayUserProfile(this.getMockUserProfile());
        }
    }

    getMockUserProfile() {
        return {
            basic_info: {
                age: "25-35岁",
                gender: "女性",
                occupation: "白领",
                travel_companion: "亲子"
            },
            tags: ["慢节奏", "趣味性", "儿童友好", "舒适型"],
            budget_preference: "舒适型",
            preferences: {
                travel_style: "深度游",
                season: "春季",
                duration: "3天"
            },
            recommendation_reason: "根据您的亲子出行需求和舒适型预算偏好，我们为您推荐了适合家庭的景点和活动，确保既有趣味性又能让孩子们开心游玩。"
        };
    }

    displayUserProfile(userProfile) {
        console.log('displayUserProfile方法被调用，数据:', userProfile);

        // 更新基本信息
        const userBasicInfo = document.getElementById('userBasicInfo');
        if (userBasicInfo && userProfile.basic_info) {
            const basicInfo = userProfile.basic_info;
            const infoText = [
                basicInfo.age,
                basicInfo.gender,
                basicInfo.occupation,
                basicInfo.travel_companion
            ].filter(Boolean).join('，');
            userBasicInfo.textContent = infoText || '暂无信息';
        }

        // 更新旅行风格
        const userTravelStyle = document.getElementById('userTravelStyle');
        if (userTravelStyle) {
            userTravelStyle.textContent = userProfile.preferences?.travel_style || userProfile.travel_style || '休闲';
        }

        // 更新兴趣标签
        const userTags = document.getElementById('userTags');
        if (userTags && userProfile.tags) {
            userTags.textContent = userProfile.tags.join('，') || '暂无标签';
        }

        // 更新偏好设置
        const userPreferences = document.getElementById('userPreferences');
        if (userPreferences && userProfile.preferences) {
            const prefs = [];
            if (userProfile.preferences.accommodation_preferences) {
                prefs.push(`住宿：${userProfile.preferences.accommodation_preferences.join('，')}`);
            }
            if (userProfile.preferences.transportation_preferences) {
                prefs.push(`交通：${userProfile.preferences.transportation_preferences.join('，')}`);
            }
            userPreferences.textContent = prefs.join('；') || '暂无特殊偏好';
        }

        // 更新预算偏好
        const userBudget = document.getElementById('userBudget');
        if (userBudget) {
            userBudget.textContent = userProfile.budget_preference || '中等';
        }

        console.log('用户画像显示完成');
    }

    displayBasicInfo(basicInfo) {
        const basicInfoTags = document.getElementById('basicInfoTags');
        if (basicInfoTags && basicInfo) {
            const tags = [];
            if (basicInfo.age) tags.push(basicInfo.age);
            if (basicInfo.gender) tags.push(basicInfo.gender);
            if (basicInfo.occupation) tags.push(basicInfo.occupation);
            if (basicInfo.travel_companion) tags.push(basicInfo.travel_companion);

            basicInfoTags.innerHTML = tags.map(tag =>
                `<span class="profile-tag basic-info">${tag}</span>`
            ).join('');
        }
    }

    displayPreferenceTags(tags) {
        const preferencesTags = document.getElementById('preferencesTags');
        if (preferencesTags && tags.length > 0) {
            preferencesTags.innerHTML = tags.map(tag =>
                `<span class="profile-tag">${tag}</span>`
            ).join('');
        }
    }

    displayBudgetPreference(budgetPreference) {
        const budgetTags = document.getElementById('budgetTags');
        if (budgetTags && budgetPreference) {
            budgetTags.innerHTML = `<span class="profile-tag budget">${budgetPreference}</span>`;
        }
    }

    displayRecommendationReason(reason) {
        const recommendationText = document.getElementById('recommendationText');
        if (recommendationText && reason) {
            recommendationText.textContent = reason;
        }
    }

    displayRealItinerary(itineraryData) {
        // 从真实数据中提取信息
        const summary = itineraryData.summary || {};
        const dailyPlans = itineraryData.daily_plans || [];
        const budgetEstimation = itineraryData.budget_estimation || {};

        // 更新行程标题和描述
        document.getElementById('itineraryTitle').textContent = summary.title || '旅行行程';
        document.getElementById('itineraryDescription').textContent = summary.description || '个性化旅行方案';

        // 更新统计信息
        document.getElementById('totalDays').textContent = summary.days || dailyPlans.length;
        document.getElementById('totalPOIs').textContent = this.countTotalPOIs(dailyPlans);

        // 格式化预算信息
        const budgetText = this.formatBudget(budgetEstimation);
        document.getElementById('estimatedBudget').textContent = budgetText;

        // 更新天气信息
        const weatherInfo = this.extractWeatherInfo(itineraryData);
        document.getElementById('weatherInfo').textContent = weatherInfo;

        this.currentItinerary = itineraryData;
    }

    countTotalPOIs(dailyPlans) {
        let total = 0;
        dailyPlans.forEach(day => {
            if (day.pois) {
                total += day.pois.length;
            }
        });
        return total;
    }

    formatBudget(budgetEstimation) {
        if (budgetEstimation.total_min && budgetEstimation.total_max) {
            return `¥${budgetEstimation.total_min}-${budgetEstimation.total_max}`;
        } else if (budgetEstimation.total_min) {
            return `¥${budgetEstimation.total_min}+`;
        }
        return '待计算';
    }

    extractWeatherInfo(itineraryData) {
        // 从工具结果中提取天气信息
        if (itineraryData.weather_forecast && itineraryData.weather_forecast.length > 0) {
            const weather = itineraryData.weather_forecast[0];
            return weather.weather || weather.dayweather || '晴';
        }
        return '晴';
    }

    showAlert(message, type = 'info') {
        // 创建Bootstrap警告框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到页面
        document.body.appendChild(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
    
    async saveItinerary() {
        if (!this.currentItinerary) return;
        
        try {
            // 这里可以实现保存逻辑
            this.showAlert('行程已保存', 'success');
            
            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('行程已保存');
            }
        } catch (error) {
            this.showAlert('保存失败: ' + error.message, 'danger');
        }
    }
    
    editItinerary() {
        if (!this.currentItinerary) return;
        
        // 这里可以实现编辑逻辑
        this.showAlert('编辑功能开发中...', 'info');
    }
    
    shareItinerary() {
        if (!this.currentItinerary) return;
        
        // 生成分享链接
        const shareUrl = `${window.location.origin}/share/${this.currentTraceId}`;
        
        if (navigator.share) {
            navigator.share({
                title: this.currentItinerary.title,
                text: this.currentItinerary.description,
                url: shareUrl
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareUrl).then(() => {
                this.showAlert('分享链接已复制到剪贴板', 'success');
            });
        }
    }
    
    async loadUserHistory() {
        // 这里可以实现加载用户历史行程的逻辑
    }
    
    showHistory() {
        // 显示历史行程模态框
        const modal = new bootstrap.Modal(document.getElementById('historyModal'));
        modal.show();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TravelPlannerAppRefactored();
});
